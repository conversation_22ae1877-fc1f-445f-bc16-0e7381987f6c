/**
 * Pipeline Window Component
 * Opens multi-step pipeline in a separate detached window for better user experience
 * Replaces modal/overlay approach with dedicated window
 */

import React, { useEffect, useRef, useState } from 'react';
import { createRoot } from 'react-dom/client';
import EnhancedPipelineVisualization, { VIEW_MODES } from './EnhancedPipelineVisualization.jsx';
import { ToastContainer, useToast } from '../../ui/feedback/ToastNotification.jsx';
import { useKeyboardShortcuts, KeyboardShortcutsHelp, KeyboardShortcutIndicator } from './KeyboardShortcuts.jsx';
import { PipelineStatusBadge, ConnectionStatus, PerformanceMetrics, ActivityIndicator } from './StatusIndicators.jsx';

/**
 * Pipeline Window Manager
 * Handles opening and managing separate windows for pipeline processing
 */
export class PipelineWindowManager {
  constructor() {
    this.windows = new Map(); // Track open windows by file ID
  }

  /**
   * Open pipeline in separate window
   * @param {Object} file - File to process
   * @param {Object} options - Pipeline options
   * @returns {Window} - Reference to opened window
   */
  openPipelineWindow(file, options = {}) {
    const fileId = file?.name || `pipeline-${Date.now()}`;

    // Close existing window for this file if open
    if (this.windows.has(fileId)) {
      this.closePipelineWindow(fileId);
    }

    // Window configuration
    const windowFeatures = [
      'width=1200',
      'height=800',
      'left=100',
      'top=100',
      'resizable=yes',
      'scrollbars=yes',
      'status=no',
      'menubar=no',
      'toolbar=no',
      'location=no',
      'directories=no'
    ].join(',');

    // Open new window
    const pipelineWindow = window.open('', `pipeline-${fileId}`, windowFeatures);

    if (!pipelineWindow) {
      console.error('Failed to open pipeline window - popup blocked?');
      return null;
    }

    // Set up window document
    this.setupWindowDocument(pipelineWindow, file, options);

    // Track window
    this.windows.set(fileId, pipelineWindow);

    // Handle window close
    pipelineWindow.addEventListener('beforeunload', () => {
      this.windows.delete(fileId);
    });

    return pipelineWindow;
  }

  /**
   * Setup window document with React app
   * @param {Window} pipelineWindow - Window reference
   * @param {Object} file - File to process
   * @param {Object} options - Pipeline options
   */
  setupWindowDocument(pipelineWindow, file, options) {
    const doc = pipelineWindow.document;

    // Set up basic HTML structure
    doc.open();
    doc.write(`
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Pipeline: ${file?.name || 'Processing'} - MVAT</title>
        <style>
          * { margin: 0; padding: 0; box-sizing: border-box; }

          body {
            font-family: Inter, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            height: 100vh;
            overflow: hidden;
            color: #1e293b;
          }

          #pipeline-root {
            width: 100%;
            height: 100vh;
            display: flex;
            flex-direction: column;
            background: white;
            border-radius: 12px;
            margin: 8px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            overflow: hidden;
          }

          .pipeline-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem 2rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
            overflow: hidden;
          }

          .pipeline-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
          }

          .pipeline-header-content {
            position: relative;
            z-index: 1;
          }

          .pipeline-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
          }

          .pipeline-subtitle {
            font-size: 0.875rem;
            opacity: 0.9;
            font-weight: 400;
          }

          .pipeline-content {
            flex: 1;
            padding: 0;
            overflow: hidden;
            background: #fafafa;
          }

          .close-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.875rem;
            font-weight: 600;
            transition: all 0.2s ease;
            backdrop-filter: blur(10px);
            position: relative;
            z-index: 1;
          }

          .close-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          }

          .close-btn:active {
            transform: translateY(0);
          }

          /* Progress bar styles */
          .progress-container {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: rgba(255, 255, 255, 0.2);
            overflow: hidden;
          }

          .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #10b981, #34d399);
            transition: width 0.3s ease;
            box-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
          }

          /* Status indicators */
          .status-bar {
            position: absolute;
            top: 1rem;
            right: 8rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            z-index: 10;
          }

          /* Activity indicator */
          .activity-indicator {
            position: absolute;
            bottom: 1rem;
            left: 1rem;
            z-index: 10;
          }

          /* Scrollbar styling */
          ::-webkit-scrollbar {
            width: 8px;
          }

          ::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 4px;
          }

          ::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 4px;
            transition: background 0.2s ease;
          }

          ::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
          }

          /* React component styling */
          #pipeline-app {
            height: 100%;
            background: #fafafa;
          }

          /* Ensure Tailwind classes work */
          .bg-white { background-color: white !important; }
          .bg-gray-50 { background-color: #f9fafb !important; }
          .bg-gray-100 { background-color: #f3f4f6 !important; }
          .bg-gray-200 { background-color: #e5e7eb !important; }
          .bg-blue-50 { background-color: #eff6ff !important; }
          .bg-blue-100 { background-color: #dbeafe !important; }
          .bg-blue-500 { background-color: #3b82f6 !important; }
          .bg-blue-600 { background-color: #2563eb !important; }
          .bg-green-50 { background-color: #f0fdf4 !important; }
          .bg-green-100 { background-color: #dcfce7 !important; }
          .bg-green-500 { background-color: #22c55e !important; }
          .bg-red-50 { background-color: #fef2f2 !important; }
          .bg-red-100 { background-color: #fee2e2 !important; }
          .bg-red-500 { background-color: #ef4444 !important; }

          .text-gray-600 { color: #4b5563 !important; }
          .text-gray-700 { color: #374151 !important; }
          .text-gray-800 { color: #1f2937 !important; }
          .text-gray-900 { color: #111827 !important; }
          .text-blue-600 { color: #2563eb !important; }
          .text-blue-700 { color: #1d4ed8 !important; }
          .text-green-600 { color: #16a34a !important; }
          .text-red-600 { color: #dc2626 !important; }

          .border { border-width: 1px !important; }
          .border-2 { border-width: 2px !important; }
          .border-gray-200 { border-color: #e5e7eb !important; }
          .border-gray-300 { border-color: #d1d5db !important; }
          .border-blue-300 { border-color: #93c5fd !important; }
          .border-green-300 { border-color: #86efac !important; }
          .border-red-300 { border-color: #fca5a5 !important; }

          .rounded { border-radius: 0.25rem !important; }
          .rounded-lg { border-radius: 0.5rem !important; }
          .rounded-xl { border-radius: 0.75rem !important; }
          .rounded-full { border-radius: 9999px !important; }

          .p-2 { padding: 0.5rem !important; }
          .p-3 { padding: 0.75rem !important; }
          .p-4 { padding: 1rem !important; }
          .px-2 { padding-left: 0.5rem !important; padding-right: 0.5rem !important; }
          .px-3 { padding-left: 0.75rem !important; padding-right: 0.75rem !important; }
          .px-4 { padding-left: 1rem !important; padding-right: 1rem !important; }
          .py-1 { padding-top: 0.25rem !important; padding-bottom: 0.25rem !important; }
          .py-2 { padding-top: 0.5rem !important; padding-bottom: 0.5rem !important; }

          .m-2 { margin: 0.5rem !important; }
          .mb-2 { margin-bottom: 0.5rem !important; }
          .mb-3 { margin-bottom: 0.75rem !important; }
          .mb-4 { margin-bottom: 1rem !important; }
          .mt-2 { margin-top: 0.5rem !important; }
          .mt-3 { margin-top: 0.75rem !important; }

          .flex { display: flex !important; }
          .flex-col { flex-direction: column !important; }
          .flex-1 { flex: 1 1 0% !important; }
          .items-center { align-items: center !important; }
          .justify-between { justify-content: space-between !important; }
          .space-x-2 > * + * { margin-left: 0.5rem !important; }
          .space-x-3 > * + * { margin-left: 0.75rem !important; }
          .space-x-4 > * + * { margin-left: 1rem !important; }
          .space-y-2 > * + * { margin-top: 0.5rem !important; }
          .space-y-3 > * + * { margin-top: 0.75rem !important; }

          .w-full { width: 100% !important; }
          .h-full { height: 100% !important; }
          .min-h-0 { min-height: 0px !important; }

          .text-sm { font-size: 0.875rem !important; line-height: 1.25rem !important; }
          .text-base { font-size: 1rem !important; line-height: 1.5rem !important; }
          .text-lg { font-size: 1.125rem !important; line-height: 1.75rem !important; }
          .text-xl { font-size: 1.25rem !important; line-height: 1.75rem !important; }
          .font-medium { font-weight: 500 !important; }
          .font-semibold { font-weight: 600 !important; }
          .font-bold { font-weight: 700 !important; }

          .shadow-sm { box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important; }
          .shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important; }

          .transition-all { transition-property: all !important; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1) !important; transition-duration: 150ms !important; }
          .duration-200 { transition-duration: 200ms !important; }
          .duration-300 { transition-duration: 300ms !important; }

          .hover\\:shadow-lg:hover { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important; }
          .hover\\:bg-gray-50:hover { background-color: #f9fafb !important; }
          .hover\\:bg-gray-100:hover { background-color: #f3f4f6 !important; }

          .overflow-hidden { overflow: hidden !important; }
          .overflow-y-auto { overflow-y: auto !important; }

          .relative { position: relative !important; }
          .absolute { position: absolute !important; }
          .fixed { position: fixed !important; }
          .inset-0 { top: 0px !important; right: 0px !important; bottom: 0px !important; left: 0px !important; }
          .top-0 { top: 0px !important; }
          .left-0 { left: 0px !important; }
          .bottom-0 { bottom: 0px !important; }
          .right-0 { right: 0px !important; }

          .z-10 { z-index: 10 !important; }
          .z-50 { z-index: 50 !important; }

          /* Animations */
          @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
          }

          @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
          }

          @keyframes bounce {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-25%); }
          }
        </style>
      </head>
      <body>
        <div id="pipeline-root">
          <div class="pipeline-header">
            <div class="pipeline-header-content">
              <h1 class="pipeline-title">
                Pipeline: ${file?.name || 'Processing'}
              </h1>
              <p class="pipeline-subtitle">
                Real-time document processing pipeline
              </p>
            </div>

            <!-- Status indicators -->
            <div class="status-bar">
              <div id="pipeline-status"></div>
              <div id="connection-status"></div>
            </div>

            <button class="close-btn" onclick="window.close()">
              ✕ Close Window
            </button>

            <div class="progress-container">
              <div class="progress-bar" id="overall-progress" style="width: 0%"></div>
            </div>

            <!-- Activity indicator -->
            <div class="activity-indicator">
              <div id="activity-status"></div>
            </div>
          </div>
          <div class="pipeline-content">
            <div id="pipeline-app"></div>
            <div id="toast-container"></div>
            <div id="keyboard-shortcuts"></div>
          </div>
        </div>
      </body>
      </html>
    `);
    doc.close();

    // Wait for document to be ready, then render React app
    setTimeout(() => {
      this.renderPipelineApp(pipelineWindow, file, options);
    }, 100);
  }

  /**
   * Render React pipeline app in window
   * @param {Window} pipelineWindow - Window reference
   * @param {Object} file - File to process
   * @param {Object} options - Pipeline options
   */
  renderPipelineApp(pipelineWindow, file, options) {
    const container = pipelineWindow.document.getElementById('pipeline-app');
    if (!container) {
      console.error('Pipeline app container not found');
      return;
    }

    // Create React root and render pipeline
    const root = createRoot(container);

    const PipelineApp = () => {
      const [overallProgress, setOverallProgress] = React.useState(0);
      const [pipelineStatus, setPipelineStatus] = React.useState('idle');
      const [isOnline, setIsOnline] = React.useState(navigator.onLine);
      const [showShortcutsHelp, setShowShortcutsHelp] = React.useState(false);
      const [performanceMetrics, setPerformanceMetrics] = React.useState({});

      // Toast system
      const toast = useToast();

      // Keyboard shortcuts
      const { activeShortcuts } = useKeyboardShortcuts({
        SHOW_SHORTCUTS: () => setShowShortcutsHelp(true),
        CLOSE_WINDOW: () => pipelineWindow.close(),
        TOGGLE_VIEW_MODE: () => {
          // This would be handled by the visualization component
          toast.info('View Mode', 'Toggled view mode');
        }
      });

      // Monitor online status
      React.useEffect(() => {
        const handleOnline = () => setIsOnline(true);
        const handleOffline = () => setIsOnline(false);

        pipelineWindow.addEventListener('online', handleOnline);
        pipelineWindow.addEventListener('offline', handleOffline);

        return () => {
          pipelineWindow.removeEventListener('online', handleOnline);
          pipelineWindow.removeEventListener('offline', handleOffline);
        };
      }, []);

      // Update progress bar and status indicators in window header
      React.useEffect(() => {
        const progressBar = pipelineWindow.document.getElementById('overall-progress');
        if (progressBar) {
          progressBar.style.width = `${overallProgress}%`;
        }
      }, [overallProgress]);

      // Render status indicators in header
      React.useEffect(() => {
        const statusContainer = pipelineWindow.document.getElementById('pipeline-status');
        const connectionContainer = pipelineWindow.document.getElementById('connection-status');
        const activityContainer = pipelineWindow.document.getElementById('activity-status');

        if (statusContainer) {
          const statusRoot = createRoot(statusContainer);
          statusRoot.render(
            React.createElement(PipelineStatusBadge, {
              status: pipelineStatus,
              progress: overallProgress
            })
          );
        }

        if (connectionContainer) {
          const connectionRoot = createRoot(connectionContainer);
          connectionRoot.render(
            React.createElement(ConnectionStatus, {
              isOnline,
              apiStatus: 'connected'
            })
          );
        }

        if (activityContainer && pipelineStatus === 'running') {
          const activityRoot = createRoot(activityContainer);
          activityRoot.render(
            React.createElement(ActivityIndicator, {
              isActive: true,
              activityType: 'processing'
            })
          );
        }
      }, [pipelineStatus, overallProgress, isOnline]);

      return (
        <div style={{
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          backgroundColor: '#f9fafb',
          fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif'
        }}>
          <div style={{
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            minHeight: 0
          }}>
            <EnhancedPipelineVisualization
              file={file}
              isProcessing={options.isProcessing || false}
              onProcessingChange={(processing, progress) => {
                options.onProcessingChange?.(processing, progress);
                if (typeof progress === 'number') {
                  setOverallProgress(progress);
                }
                setPipelineStatus(processing ? 'running' : 'idle');
              }}
              onStepComplete={(stepId, result) => {
                options.onStepComplete?.(stepId, result);
                toast.success('Step Completed', `${stepId} finished successfully`);
              }}
              onError={(error) => {
                options.onError?.(error);
                setPipelineStatus('error');
                toast.error('Pipeline Error', error.message || 'An error occurred');
              }}
              autoRun={options.autoRun !== false}
              initialViewMode={VIEW_MODES.DETAILED} // Always use detailed view in separate window
              windowMode={true} // Flag to indicate window mode
            />
          </div>

          {/* Toast notifications */}
          <ToastContainer
            toasts={toast.toasts}
            onRemoveToast={toast.removeToast}
            position="top-right"
          />

          {/* Keyboard shortcuts help */}
          <KeyboardShortcutsHelp
            isOpen={showShortcutsHelp}
            onClose={() => setShowShortcutsHelp(false)}
          />

          {/* Active shortcuts indicator */}
          <KeyboardShortcutIndicator activeShortcuts={activeShortcuts} />
        </div>
      );
    };

    root.render(<PipelineApp />);
  }

  /**
   * Close pipeline window for specific file
   * @param {string} fileId - File identifier
   */
  closePipelineWindow(fileId) {
    const pipelineWindow = this.windows.get(fileId);
    if (pipelineWindow && !pipelineWindow.closed) {
      pipelineWindow.close();
    }
    this.windows.delete(fileId);
  }

  /**
   * Close all pipeline windows
   */
  closeAllWindows() {
    for (const [fileId, pipelineWindow] of this.windows) {
      if (pipelineWindow && !pipelineWindow.closed) {
        pipelineWindow.close();
      }
    }
    this.windows.clear();
  }

  /**
   * Get window for specific file
   * @param {string} fileId - File identifier
   * @returns {Window|null} - Window reference or null
   */
  getWindow(fileId) {
    const pipelineWindow = this.windows.get(fileId);
    return (pipelineWindow && !pipelineWindow.closed) ? pipelineWindow : null;
  }

  /**
   * Check if window is open for file
   * @param {string} fileId - File identifier
   * @returns {boolean} - True if window is open
   */
  isWindowOpen(fileId) {
    return this.getWindow(fileId) !== null;
  }
}

// Global instance
export const pipelineWindowManager = new PipelineWindowManager();

/**
 * React Hook for Pipeline Window Management
 * @returns {Object} - Window management functions
 */
export function usePipelineWindow() {
  const [openWindows, setOpenWindows] = useState(new Set());

  const openWindow = (file, options = {}) => {
    const fileId = file?.name || `pipeline-${Date.now()}`;
    const pipelineWindow = pipelineWindowManager.openPipelineWindow(file, options);

    if (pipelineWindow) {
      setOpenWindows(prev => new Set([...prev, fileId]));

      // Listen for window close
      pipelineWindow.addEventListener('beforeunload', () => {
        setOpenWindows(prev => {
          const newSet = new Set(prev);
          newSet.delete(fileId);
          return newSet;
        });
      });
    }

    return pipelineWindow;
  };

  const closeWindow = (fileId) => {
    pipelineWindowManager.closePipelineWindow(fileId);
    setOpenWindows(prev => {
      const newSet = new Set(prev);
      newSet.delete(fileId);
      return newSet;
    });
  };

  const closeAllWindows = () => {
    pipelineWindowManager.closeAllWindows();
    setOpenWindows(new Set());
  };

  const isWindowOpen = (fileId) => {
    return openWindows.has(fileId) && pipelineWindowManager.isWindowOpen(fileId);
  };

  return {
    openWindow,
    closeWindow,
    closeAllWindows,
    isWindowOpen,
    openWindows: Array.from(openWindows)
  };
}

export default PipelineWindowManager;
